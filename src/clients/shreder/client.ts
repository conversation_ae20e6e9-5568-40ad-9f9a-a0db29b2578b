import type { Metadata } from '@grpc/grpc-js'
import { type CreateGrpcClientOptions, type CreateStreamIteratorOptions, createDuplexStreamIterator, createGrpcClient, createReadableStream } from '../../utils'
import { type Entry, ShrederServiceClient, type SubscribeEntriesRequest } from './generated/shredstream'

export type ShrederClientOptions = CreateGrpcClientOptions & CreateStreamIteratorOptions<Entry, Entry>

export class ShrederClient {
    protected readonly grpc: ShrederServiceClient

    public constructor(url: string, protected readonly options: ShrederClientOptions = {}) {
        this.grpc = createGrpcClient(ShrederServiceClient, url, options)
    }

    public async subscribeEntries(request: SubscribeEntriesRequest, metadata?: Metadata) {
        return createReadableStream(() => this.grpc.subscribeEntries(request, metadata), this.options)
    }

    public async subscribeTransactions() {
        return createDuplexStreamIterator(() => this.grpc.subscribeTransactions(), {})
    }
}
